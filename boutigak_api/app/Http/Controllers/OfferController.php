<?php

namespace App\Http\Controllers;
use Carbon\Carbon;
use App\Models\Item;
use App\Models\User;
use App\Models\Store;
use App\Models\Discution;
use App\Models\Notification;
use App\services\FCMService;
use Illuminate\Http\Request;
use App\Models\DiscutionMessage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class OfferController extends Controller
{
    protected FCMService $firebaseService;

    public function __construct(FCMService $firebaseService)
    {
        $this->firebaseService = $firebaseService;
    }


    private function getNotificationContent(string $type, array $params = []): array
{
    // Helper function to format Arabic text with proper RTL handling
    $formatArabic = function(string $text, array $values = []): string {
        return vsprintf(str_replace('%s', '%s', $text), array_map(fn($v) => $v ?: '', $values));
    };

    $messages = [
        'new_message' => [
            'en' => [
                'title' => 'New Message',
                'body' => sprintf("New message from %s", $params['sender_name'])
            ],
            'fr' => [
                'title' => 'Nouveau Message',
                'body' => sprintf("Nouveau message de %s", $params['sender_name'])
            ],
            'ar' => [
                'title' => 'رسالة جديدة',
                'body' => $formatArabic("%s رسالة جديدة من", [$params['sender_name']])
            ]
        ],
        'new_offer' => [
            'en' => [
                'title' => 'New Offer',
                'body' => sprintf("%s made an offer: %s", $params['sender_name'], $params['price'])
            ],
            'fr' => [
                'title' => 'Nouvelle Offre',
                'body' => sprintf("%s a fait une offre: %s", $params['sender_name'], $params['price'])
            ],
            'ar' => [
                'title' => 'عرض جديد',
                'body' => $formatArabic("%s :%s قدم عرضًا", [$params['price'], $params['sender_name']])
            ]
        ],
        'store_message' => [
            'en' => [
                'title' => 'Store Message',
                'body' => sprintf("New message from store %s", $params['store_name'])
            ],
            'fr' => [
                'title' => 'Message du Magasin',
                'body' => sprintf("Nouveau message du magasin %s", $params['store_name'])
            ],
            'ar' => [
                'title' => 'رسالة من المتجر',
                'body' => $formatArabic("%s رسالة جديدة من متجر", [$params['store_name']])
            ]
        ]
    ];

    return $messages[$type] ?? [];
}

private function sendLocalizedNotification(User $recipient, string $type, array $params, ?int $discussionId = null): void
{
    // Set default values for params
    $params = array_merge([
        'sender_name' => '',
        'price' => '',
        'content' => '',
        'discussion_id' => $discussionId,
        'store_name' => ''
    ], array_filter($params));

    $token = $recipient->deviceToken->token ?? null;
    $userLang = $recipient->lang ?? 'en'; // Default to English if no language set

    try {
        // Get all localized messages
        $messages = $this->getNotificationContent($type, $params);

        // Get user's preferred language message for push notification
        $userMessage = $messages[$userLang] ?? $messages['en']; // Fallback to English

        // // Create notification with all languages
        // $notification = Notification::create([
        //     'user_id' => $recipient->id,
        //     'type' => $type,
        //     'discussion_id' => $discussionId,
        //     'is_read' => false,
        //     // Store the title in all languages
        //     'title_en' => $messages['en']['title'],
        //     'title_fr' => $messages['fr']['title'],
        //     'title_ar' => $messages['ar']['title'],
        //     // Store the message in all languages
        //     'message_en' => $messages['en']['body'],
        //     'message_fr' => $messages['fr']['body'],
        //     'message_ar' => $messages['ar']['body'],
        //     // Store the current language version for backward compatibility
        //     // 'title' => $userMessage['title'],
        //     // 'message' => $userMessage['body'],
        //     // Store parameters for future reference
        //     'params' => json_encode($params)
        // ]);

        // Send push notification in user's preferred language
        if ($token) {
              $this->firebaseService->sendMessageNotification(
            $token,
            $userMessage['title'],
            $userMessage['body'],
            discussionId: $discussionId // Pass discussion ID for message notifications
        );


            Log::info('Notification sent and stored successfully', [
                'recipient_id' => $recipient->id,
                'language' => $userLang,
                'type' => $type,
            ]);
        } else {
            Log::warning('No device token found for recipient, notification stored in database only', [
                'recipient_id' => $recipient->id
            ]);
        }
    } catch (\Exception $e) {
        Log::error('Failed to process notification', [
            'error' => $e->getMessage(),
            'recipient_id' => $recipient->id,
            'type' => $type,
            'params' => $params
        ]);
    }
}
    /**
     * @OA\Post(
     *     path="/discussions",
     *     summary="Create a new discussion",
     *     tags={"Discussions"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"item_id"},
     *             @OA\Property(property="item_id", type="integer", example=1),
     *             @OA\Property(property="store_id", type="integer", nullable=true, example=1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Discution created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="discussion", type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="item_id", type="integer"),
     *                 @OA\Property(property="buyer_id", type="integer"),
     *                 @OA\Property(property="seller_id", type="integer"),
     *                 @OA\Property(property="store_id", type="integer", nullable=true),
     *                 @OA\Property(property="created_at", type="string", format="date-time"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid request",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="errors", type="object")
     *         )
     *     )
     * )
     */
    public function createDiscussion(Request $request)
    {
        $request->validate([
            'item_id' => 'required|integer|exists:item,id',
            'is_store_discussion' => 'boolean',
            'store_id' => 'integer|exists:store,id',
        ]);

        $user = auth()->user();
        $itemId = $request->input('item_id');
        $isStoreDiscussion = $request->input('is_store_discussion', false);
        $storeId = $request->input('store_id');

        // Get the item to determine seller
        $item = Item::find($itemId);
        if (!$item) {
            return response()->json(['message' => 'Item not found'], 404);
        }

        // Check if a discussion already exists for this item and user
        $existingDiscussion = Discution::where('item_id', $itemId)
            ->where(function ($query) use ($user, $isStoreDiscussion, $storeId) {
                if ($isStoreDiscussion) {
                    $query->where('is_store_discussion', true)
                          ->where('store_id', $storeId)
                          ->where('buyer_id', $user->id);
                } else {
                    $query->where('buyer_id', $user->id)
                          ->where('is_store_discussion', false);
                }
            })
            ->first();

        if ($existingDiscussion) {
            return response()->json([
                'message' => 'Discution already exists',
                'discussion' => $existingDiscussion
            ]);
        }



        $ownerStoreID = Store::find( $storeId)->user_id;


        Log::info('store id ' . $ownerStoreID);


        $discussion = new Discution();
        $discussion->buyer_id = $user->id;
        $discussion->seller_id =  $isStoreDiscussion ? $ownerStoreID : $item->user_id;
        $discussion->item_id = $itemId;
        $discussion->is_store_discussion = $isStoreDiscussion;

        if ($isStoreDiscussion && $storeId) {
            $discussion->store_id = $storeId;
        }

        $discussion->save();

        // Get the item details
        $item = Item::with('user', 'images')->find($itemId);

        return response()->json([
            'message' => 'Discution created successfully',
            'discussion' => $discussion,
            'item' => $item
        ], 201);
    }

    /**
     * @OA\Post(
     *     path="/discussions/{discussionId}/messages",
     *     summary="Send a message in a discussion",
     *     tags={"Messages"},
     *     @OA\Parameter(
     *         name="discussionId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="content", type="string", example="Hello!"),
     *             @OA\Property(property="is_an_offer", type="boolean", example=false),
     *             @OA\Property(property="price", type="number", format="float", example=100.0),
     *             @OA\Property(property="reply_discution_id", type="integer", nullable=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Message sent successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="discution_id", type="integer"),
     *                 @OA\Property(property="sender_id", type="integer"),
     *                 @OA\Property(property="content", type="string"),
     *                 @OA\Property(property="is_an_offer", type="boolean"),
     *                 @OA\Property(property="price", type="number", format="float"),
     *                 @OA\Property(property="reply_discution_id", type="integer", nullable=true),
     *                 @OA\Property(property="created_at", type="string", format="date-time"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time")
     *             ),
     *             @OA\Property(property="sender", type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="firstname", type="string"),
     *                 @OA\Property(property="lastname", type="string")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Discution not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="errors", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string")
     *         )
     *     )
     * )
     */
    public function sendMessage(Request $request, $discussionID)
    {
        Log::info('request ' . json_encode($request->all()));
        $request->validate([
            'content' => 'required_without:is_an_offer|string',
            'is_an_offer' => 'boolean',
            'price' => 'required_if:is_an_offer,true|numeric',
            'is_store_discussion' => 'boolean',
        ]);

        $user = auth()->user();
        $isOffer = $request->input('is_an_offer', false);
        $isStoreDiscussion = $request->input('is_store_discussion', false);

        Log::info('is_store_discussion ' . $isStoreDiscussion);
        Log::info('is_offer ' . $isOffer);
        Log::info('user ' . json_encode($user));
        Log::info('discussion_id ' . $discussionID);

        // Find the discussion by ID
        $discussion = Discution::find($discussionID);

        if (!$discussion) {
            return response()->json(['message' => 'Discution not found'], 404);
        }

        // Create a new message
        $message = new DiscutionMessage();
        $message->discution_id = $discussion->id;
        $message->sender_id = $user->id;

        if ($isOffer) {
            $message->is_an_offer = true;
            $message->price = $request->input('price');
            $message->content = "Offer: " . $request->input('price');
        } else {
            $message->content = $request->input('content');
        }

        $message->is_store_discussion = $isStoreDiscussion;
        $message->save();

        // Get the item and recipient for notification
        $item = Item::find($discussion->item_id);

        $recipient = null;
        if ($user->id == $discussion->buyer_id) {
            // If buyer is sending, notify seller
            $recipient = User::find($discussion->seller_id);


        } else {
            // If seller is sending, notify buyer
            $recipient = User::find($discussion->buyer_id);
        }



        Log::info('recipient ' . json_encode($recipient));
        if ($recipient && $recipient->id !== $user->id) {
            // Send notification based on message type
            if ($isOffer) {
                $this->sendLocalizedNotification(
                    $recipient,
                    'new_offer',
                    [
                        'sender_name' => $user->firstname . ' ' . $user->lastname,
                        'price' => $message->price
                    ],
                    $discussion->id
                );
            } else {
                $notificationType = $isStoreDiscussion ? 'store_message' : 'new_message';
                $params = $isStoreDiscussion
                    ? ['store_name' => Store::find($discussion->store_id)->name]
                    : ['sender_name' => $user->firstname . ' ' . $user->lastname];

                $this->sendLocalizedNotification(
                    $recipient,
                    $notificationType,
                    $params,
                    $discussion->id
                );
            }
        }

        return response()->json([
            'message' => 'Message sent successfully',
            'data' => $message
        ], 201);
    }

    /**
     * @OA\Get(
     *     path="/discussions",
     *     summary="Get all discussions for the authenticated user",
     *     tags={"Discussions"},
     *     @OA\Response(
     *         response=200,
     *         description="List of discussions",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     **                 @OA\Property(property="id", type="integer"),
                 *                 @OA\Property(property="item_id", type="integer"),
                 *                 @OA\Property(property="buyer_id", type="integer"),
                 *                 @OA\Property(property="seller_id", type="integer"),
                 *                 @OA\Property(property="store_id", type="integer", nullable=true),
                 *                 @OA\Property(property="created_at", type="string", format="date-time"),
                 *                 @OA\Property(property="updated_at", type="string", format="date-time")
                 *             )
                 *         )
                 *     ),
                 *     @OA\Response(
                 *         response=500,
                 *         description="Server error",
                 *         @OA\JsonContent(
                 *             @OA\Property(property="error", type="string")
                 *         )
                 *     )
                 * )
                 */
    /**
     * @OA\Get(
     *     path="/discussions",
     *     summary="Get all discussions for the authenticated user with pagination",
     *     tags={"Discussions"},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", default=15)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of discussions with pagination metadata",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="array", @OA\Items(type="object")),
     *             @OA\Property(property="pagination", type="object",
     *                 @OA\Property(property="current_page", type="integer"),
     *                 @OA\Property(property="last_page", type="integer"),
     *                 @OA\Property(property="per_page", type="integer"),
     *                 @OA\Property(property="total", type="integer")
     *             )
     *         )
     *     )
     * )
     */
    public function getUserDiscussions(Request $request)
{
    $user = auth()->user();
    if (!is_object($user) || !isset($user->id)) {
        return response()->json(['message' => 'Unauthorized'], 401);
    }
    
    $userId = $user->id;
    $perPage = $request->input('per_page', 10);
    $page = $request->input('page', 1);

    // Get store IDs for the user
    $storeIds = Store::where('user_id', $userId)->pluck('id')->toArray();

    $discussions = Discution::with([
            'item' => function($query) {
                $query->select('id', 'title', 'title_ar', 'user_id', 'store_id' , 'price');
            },
            'item.images' => function($query) {
                $query->select('media_id', 'item_id', 'url', 'aspect_ratio');
            },
            'buyer' => function($query) {
                $query->select('id', 'firstname', 'lastname', 'phone');
            },
            'seller' => function($query) {
                $query->select('id', 'firstname', 'lastname', 'phone');
            },
            'store' => function($query) {
                $query->select('id', 'name', 'user_id');
            },
            'store.user' => function($query) {
                $query->select('id', 'firstname', 'lastname', 'phone');
            }
        ])
        ->where(function($query) use ($userId, $storeIds) {
            // Regular discussions where user is buyer or seller
            $query->where(function($subQuery) use ($userId) {
                $subQuery->where('buyer_id', $userId)
                         ->orWhere('seller_id', $userId);
            });
            
            // OR store discussions where user owns the store
            if (!empty($storeIds)) {
                $query->orWhere(function($subQuery) use ($storeIds) {
                    $subQuery->where('is_store_discussion', true)
                             ->whereIn('store_id', $storeIds);
                });
            }
        })
        ->whereHas('messages') // Only get discussions with messages
        ->orderBy('updated_at', 'desc')
        ->paginate($perPage, ['*'], 'page', $page);

    // Get discussion IDs for fetching latest messages separately
    $discussionIds = collect($discussions->items())->pluck('id')->toArray();

    // Fetch latest messages separately to avoid join ambiguity
    $latestMessages = DiscutionMessage::select('id', 'discution_id', 'content', 'is_an_offer', 'price', 'created_at', 'sender_id', 'is_read')
        ->whereIn('discution_id', $discussionIds)
        ->whereIn('id', function($query) use ($discussionIds) {
            $query->select(\DB::raw('MAX(id)'))
                  ->from('discution_message')
                  ->whereIn('discution_id', $discussionIds)
                  ->groupBy('discution_id');
        })
        ->get()
        ->keyBy('discution_id');

    $unreadCounts = DiscutionMessage::whereIn('discution_id', $discussionIds)
        ->where('is_read', false)
        ->where('sender_id', '!=', $userId)
        ->selectRaw('discution_id, COUNT(*) as unread_count')
        ->groupBy('discution_id')
        ->pluck('unread_count', 'discution_id');

    // Format discussions
    $formattedDiscussions = collect($discussions->items())
        ->map(function ($discussion) use ($userId, $latestMessages, $unreadCounts) {
            $item = $discussion->item;
            $latestMessage = $latestMessages->get($discussion->id);

            $interlocutor = null;
            if ($discussion->is_store_discussion) {

                if ($discussion->seller->id == $userId){
                    $interlocutor = $discussion->buyer;
                }
                else {
                    $interlocutor = $discussion->store;
                }

            } else if ($discussion->buyer_id == $userId) {
                $interlocutor = $discussion->seller;
            } else {
                $interlocutor = $discussion->buyer;
            }

            return [
                'id' => $discussion->id,
                'item' => $item,
                'latest_message' => $latestMessage ? [
                    'id' => $latestMessage->id,
                    'content' => $latestMessage->content,
                    'is_an_offer' => $latestMessage->is_an_offer,
                    'price' => $latestMessage->price,
                    'created_at' => $latestMessage->created_at,
                    'sender_id' => $latestMessage->sender_id,
                    'is_read' => $latestMessage->is_read // Add this
                ] : null,
                'unread_count' => $unreadCounts[$discussion->id] ?? 0, // Add this
                'interlocutor' => [
                    'id' => $interlocutor->id ?? null,
                    'name' => $interlocutor->name ?? ($interlocutor->firstname ?? '') . ' ' . ($interlocutor->lastname ?? ''),
                    'avatar' => $interlocutor->avatar ?? null,
                    'phone' => $this->getInterlocutorPhone($interlocutor, $discussion)
                ],
                'is_store_discussion' => $discussion->is_store_discussion,
                'created_at' => $discussion->created_at,
                'seller' => $discussion->seller ? [
                    'id' => $discussion->seller->id,
                    'firstname' => $discussion->seller->firstname,
                    'lastname' => $discussion->seller->lastname,
                    'phone' => $discussion->seller->phone
                ] : null,
                'buyer' => $discussion->buyer ? [
                    'id' => $discussion->buyer->id,
                    'firstname' => $discussion->buyer->firstname,
                    'lastname' => $discussion->buyer->lastname,
                    'phone' => $discussion->buyer->phone
                ] : null,
                'store' => $discussion->store ? [
                    'id' => $discussion->store->id,
                    'name' => $discussion->store->name,
                    'owner_phone' => $discussion->store->user->phone ?? null
                ] : null
            ];
        })
        ->sortByDesc(function ($discussion) {
            return $discussion['latest_message'] ? $discussion['latest_message']['created_at'] : $discussion['created_at'];
        })
        ->values();

    return response()->json([
        'data' => $formattedDiscussions,
        'pagination' => [
            'current_page' => $discussions->currentPage(),
            'last_page' => $discussions->lastPage(),
            'per_page' => $discussions->perPage(),
            'total' => $discussions->total()
        ]
    ]);
}

/**
 * Get the appropriate phone number for the interlocutor based on discussion type
 */
private function getInterlocutorPhone($interlocutor, $discussion)
{
    // If it's a store discussion and the interlocutor is the store
    if ($discussion->is_store_discussion && isset($interlocutor->name)) {
        // This is a store, get the store owner's phone
        return $discussion->store->user->phone ?? null;
    }

    // For regular discussions or when interlocutor is a user (buyer/seller)
    return $interlocutor->phone ?? null;
}
       /**
     * Group messages by date categories (Today, Yesterday, and specific dates)
     *
     * @param \Illuminate\Support\Collection $messages
     * @return array
     */
    private function groupMessagesByDate($messages)
    {
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();

        $groupedMessages = [];

        foreach ($messages as $message) {
            $messageDate = Carbon::parse($message->created_at);

            if ($messageDate->isToday()) {
                $key = 'Today';
            } elseif ($messageDate->isYesterday()) {
                $key = 'Yesterday';
            } else {
                $key = $messageDate->format('F j, Y');
            }

            if (!isset($groupedMessages[$key])) {
                $groupedMessages[$key] = [];
            }

            $groupedMessages[$key][] = $message;
        }

        $sortedMessages = [];

        // First add Today if it exists
        if (isset($groupedMessages['Today'])) {
            // Reverse the messages within Today group (newest first)
            $sortedMessages['Today'] = array_reverse($groupedMessages['Today']);
        }

        // Then add Yesterday if it exists
        if (isset($groupedMessages['Yesterday'])) {
            // Reverse the messages within Yesterday group (newest first)
            $sortedMessages['Yesterday'] = array_reverse($groupedMessages['Yesterday']);
        }

        // Get the remaining date keys and sort them in descending order
        $dateKeys = array_filter(array_keys($groupedMessages), function($key) {
            return $key !== 'Today' && $key !== 'Yesterday';
        });

        // Sort dates in descending order (newest first)
        usort($dateKeys, function($a, $b) {
            return strtotime($b) - strtotime($a);
        });

        // Add the sorted dates to the result
        foreach ($dateKeys as $key) {
            // Reverse the messages within each date group (newest first)
            $sortedMessages[$key] = array_reverse($groupedMessages[$key]);
        }

        return $sortedMessages;
    }

    /**
     * @OA\Get(
     *     path="/messages/{itemId}",
     *     summary="Get all messages for a specific item grouped by date",
     *     tags={"Messages"},
     *     @OA\Parameter(
     *         name="itemId",
     *         in="path",
     *         required=true,
     *         description="ID of the item to get messages for",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of discussions with messages grouped by date",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="item_id", type="integer"),
     *                 @OA\Property(property="buyer_id", type="integer"),
     *                 @OA\Property(property="seller_id", type="integer"),
     *                 @OA\Property(property="store_id", type="integer", nullable=true),
     *                 @OA\Property(property="is_store_discussion", type="boolean"),
     *                 @OA\Property(property="store_details", type="object", nullable=true),
     *                 @OA\Property(property="messages_by_date", type="object",
     *                     @OA\AdditionalProperties(
     *                         type="array",
     *                         @OA\Items(
     *                             @OA\Property(property="id", type="integer"),
     *                             @OA\Property(property="content", type="string"),
     *                             @OA\Property(property="sender_id", type="integer"),
     *                             @OA\Property(property="is_an_offer", type="boolean"),
     *                             @OA\Property(property="price", type="number"),
     *                             @OA\Property(property="sent_by_me", type="boolean"),
     *                             @OA\Property(property="sender_details", type="object"),
     *                             @OA\Property(property="created_at", type="string"),
     *                             @OA\Property(property="updated_at", type="string")
     *                         )
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="No discussions found"
     *     )
     * )
     */
    public function getMessages($discussionID)
    {
        $userId = Auth::id();

        $discussion = Discution::with([
                'messages' => function($query) {
                    $query->orderBy('created_at', 'asc')
                          ->select('id', 'discution_id', 'sender_id', 'content', 'is_an_offer', 'price', 'created_at');
                },
                'messages.sender:id,firstname,lastname',
                'buyer:id,firstname,lastname,phone',
                'seller:id,firstname,lastname,phone',
                'store:id,name,description'
            ])
            ->select('id', 'buyer_id', 'seller_id', 'store_id', 'item_id', 'is_store_discussion', 'created_at', 'updated_at')
            ->firstWhere('id', $discussionID);

        if (!$discussion) {
            return response()->json(['message' => 'Discussion not found'], 404);
        }

        // Process messages efficiently
        $discussion->messages->each(function($message) use ($userId) {
            $message->sent_by_me = $message->sender_id == $userId;
            $message->makeHidden(['sender']);
        });

        $discussion->messages_by_date = $this->groupMessagesByDate($discussion->messages);
        unset($discussion->messages);

        if ($discussion->store_id) {
            $discussion->is_store_discussion = true;
            $discussion->store_details = [
                'id' => $discussion->store->id,
                'name' => $discussion->store->name,
                'description' => $discussion->store->description
            ];
        } else {
            $discussion->is_store_discussion = false;
        }

        return response()->json($discussion);
    }


/**
 * @OA\Post(
 *     path="/discussions/{discussionId}/mark-read",
 *     summary="Mark all messages in a discussion as read for the authenticated user",
 *     tags={"Messages"},
 *     @OA\Parameter(
 *         name="discussionId",
 *         in="path",
 *         required=true,
 *         @OA\Schema(type="integer")
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="Messages marked as read",
 *         @OA\JsonContent(
 *             @OA\Property(property="success", type="boolean"),
 *             @OA\Property(property="updated_count", type="integer")
 *         )
 *     ),
 *     @OA\Response(
 *         response=404,
 *         description="Discussion not found"
 *     )
 * )
 */
public function markMessagesAsRead($discussionId)
{
    $userId = Auth::id();

    $discussion = Discution::find($discussionId);
    if (!$discussion) {
        return response()->json(['message' => 'Discussion not found'], 404);
    }

    // Mark all messages as read where sender is not the current user
    $updated = DiscutionMessage::where('discution_id', $discussionId)
        ->where('sender_id', '!=', $userId)
        ->where('is_read', false)
        ->update(['is_read' => true]);

          return response()->json([
        'success' => true,
        'updated_count' => $updated
    ]);
}
}
