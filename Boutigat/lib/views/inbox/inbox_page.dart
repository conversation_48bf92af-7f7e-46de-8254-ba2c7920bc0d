import 'dart:async';
import 'dart:developer';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/notifications_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:boutigak/controllers/inbox_controller.dart';
import 'package:get/get.dart';
import 'package:boutigak/views/widgets/customappbar_widget.dart';
import 'package:intl/intl.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:boutigak/data/services/conversation_service.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/data/models/messages.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:boutigak/controllers/badge_controller.dart';

class InboxPage extends StatefulWidget {
  @override
  State<InboxPage> createState() => _InboxPageState();
}

class _InboxPageState extends State<InboxPage> {
  final InboxController inboxController = Get.put(InboxController());
  final AuthController authController = Get.put(AuthController());
  final BadgeController badgeController = Get.find<BadgeController>();

  @override
  void initState() {
    super.initState();
    // Reset badges when viewing the inbox
    // if (inboxController.notificationsSelected.value) {
    //   badgeController.resetBadge('notifications');
    // } else {
    //   badgeController.resetBadge('messages');
    // }
  }

  // Function to refresh discussions
  Future<void> _refreshDiscussions() async {
    await inboxController.refreshDiscussions();
    await badgeController.fetchBadgeCounts();
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);
    return Scaffold(
      appBar: CustomAppBar(
        titleText: "inbox".tr,
        icon: FontAwesomeIcons.solidUser,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              Expanded(
                child: Obx(() => CustomTabButton(
                      text: 'messages'.tr,
                      isSelected: inboxController.messagesSelected.value,
                      onPressed: () {
                        inboxController.selectMessages();
                        badgeController.resetBadge('messages');
                      },
                      badgeCount: badgeController.getModuleCount('messages'),
                    )),
              ),
              Expanded(
                child: Obx(() => CustomTabButton(
                      text: 'notifications'.tr,
                      isSelected: inboxController.notificationsSelected.value,
                      onPressed: () {
                        inboxController.selectNotifications();
                        badgeController.resetBadge('notifications');
                      },
                      badgeCount:
                          badgeController.getModuleCount('notifications'),
                    )),
              ),
            ],
          ),
          Expanded(
            child: Obx(
              () => inboxController.isLoading.value
                  ? const Center(
                      child: CupertinoTheme(
                        data: CupertinoThemeData(
                          brightness: Brightness.light,
                        ),
                        child: CupertinoActivityIndicator(radius: 18),
                      ),
                    )
                  : RefreshIndicator(
                      onRefresh: _refreshDiscussions,
                      child: inboxController.messagesSelected.value
                          ? MessageList()
                          : NotificationList(),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}

class MessageList extends StatefulWidget {
  @override
  State<MessageList> createState() => _MessageListState();
}

class _MessageListState extends State<MessageList> {
  final InboxController inboxController = Get.find();
  final BadgeController badgeController = Get.find<BadgeController>();
  @override
  void initState() {
    super.initState();
    // Ensure discussions are loaded when the widget is created
    // inboxController.fetchDiscussions();
  }

  // Function to make a phone call
  void _makePhoneCall(String phoneNumber) async {
    final Uri url = Uri.parse('tel:$phoneNumber');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      Get.snackbar(
        'Error',
        'Could not launch phone call',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> _refreshDiscussions() async {
    await inboxController.refreshDiscussions();
    await badgeController.fetchBadgeCounts();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Sort discussions by latest message timestamp
      final sortedDiscussions = List.from(inboxController.discussions)
        ..sort((a, b) {
          final aTime = a['latest_message'] != null
              ? DateTime.parse(a['latest_message']['created_at'])
              : DateTime.parse(a['created_at']);
          final bTime = b['latest_message'] != null
              ? DateTime.parse(b['latest_message']['created_at'])
              : DateTime.parse(b['created_at']);
          return bTime.compareTo(aTime); // Descending order (newest first)
        });

      if (inboxController.isLoading.value && sortedDiscussions.isEmpty) {
        return const Center(
          child: CupertinoTheme(
            data: CupertinoThemeData(
              brightness: Brightness.light,
            ),
            child: CupertinoActivityIndicator(radius: 18),
          ),
        );
      }

      // Show empty state if no discussions
      if (sortedDiscussions.isEmpty) {
        return RefreshIndicator(
          onRefresh: _refreshDiscussions,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.chat_bubble_outline,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No messages yet',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Start a conversation by messaging a seller',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: _refreshDiscussions,
        child: ListView.builder(
          controller: inboxController.scrollController,
          itemCount: sortedDiscussions.length +
              (inboxController.hasMoreDiscussions.value ? 1 : 0),
          itemBuilder: (context, index) {
          if (index == sortedDiscussions.length) {
              return Padding(
                padding: const EdgeInsets.all(16.0),
                child: Center(
                  child: CupertinoTheme(
                    data: CupertinoThemeData(
                      brightness: Brightness.light,
                    ),
                    child: CupertinoActivityIndicator(radius: 15),
                  ),
                ),
              );
            }

            final item = sortedDiscussions[index];
            final connectedUserId = Get.find<AuthController>().user?.id;

            final buyerID = item['buyer']['id'] ?? null;
            final sellerID = item['seller']['id'] ?? null;

            log('buyer id $buyerID');
            log('item in convo  $item');
            // log('store in convo  ');

            log('seller id $sellerID');
            final user =
                buyerID == connectedUserId ? item['seller'] : item['buyer'];

            final int unreadCount = item['unread_count'] ?? 0;
  

            return Column(
              children: [
                ListTile(
                  leading: CircleAvatar(
                    radius: 24,
                    backgroundImage: NetworkImage(
                      '${item['item']['images'][0]['url']}',
                    ),
                  ),
                  title: Row(
                    children: [
                      Text(' ${item['interlocutor']['name']}'),
                      Spacer(),
                      if (user != null && user['phone'] != null) Container(),
                    ],
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item['latest_message']?['is_an_offer'] == true
                            ? (item['latest_message']['sent_by_me'] == true
                                ? 'You made an offer'
                                : 'You received an offer')
                            : '${item['latest_message']?['content'] ?? ''}',
                        style: TextStyle(
                          color: unreadCount > 0 ? Colors.black : Colors.grey,
                          fontWeight: unreadCount > 0
                              ? FontWeight.w900
                              : FontWeight.w100,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                  trailing: unreadCount > 0
                      ? Container(
                          padding: EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: Text(
                            unreadCount.toString(),
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold),
                          ),
                        )
                      : null,
                  onTap: () => inboxController.openConversation(
                    item,
                    item['id'],
                    user['firstname'] + " " + user['lastname'],
                    item['is_store_discussion'],
                  ),
                ),
                const Divider(
                  height: 1,
                  thickness: 1,
                  indent: 72, // align with avatar start
                  endIndent: 16,
                  color: Color(0xFFE0E0E0),
                ),
              ],
            );
          },
        ),
      );
    });
  }
}

class NotificationList extends StatefulWidget {
  @override
  State<NotificationList> createState() => _NotificationListState();
}

class _NotificationListState extends State<NotificationList> {
  final NotificationController notificationController =
      Get.put(NotificationController());
  final BadgeController badgeController = Get.find<BadgeController>();
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    notificationController.fetchNotifications();
    badgeController.resetBadge('notifications');
    scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    scrollController.removeListener(_scrollListener);
    scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (scrollController.position.pixels >=
            scrollController.position.maxScrollExtent - 200 &&
        !notificationController.isLoading.value &&
        notificationController.currentPage.value * 15 <
            notificationController.totalNotifications.value) {
      notificationController.loadMoreNotifications();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (notificationController.isLoading.isTrue &&
          notificationController.notifications.isEmpty) {
        return const Center(
          child: CupertinoTheme(
            data: CupertinoThemeData(
              brightness: Brightness.light,
            ),
            child: CupertinoActivityIndicator(radius: 15),
          ),
        );
      } else if (notificationController.isError.isTrue &&
          notificationController.notifications.isEmpty) {
        return Center(
            child: Text('Erreur lors du chargement des notifications'));
      } else if (notificationController.notifications.isEmpty) {
        // ✅ Affichage "aucune notification"
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.notifications_none,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'No notifications yet',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'You’ll be notified when something important happens',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      } else {
        // ✅ Liste des notifications
        return ListView.builder(
          controller: scrollController,
          itemCount: notificationController.notifications.length + 1,
          itemBuilder: (context, index) {
            if (index < notificationController.notifications.length) {
              final notification = notificationController.notifications[index];
              return ListTile(
                leading: notification.image != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(10.0),
                        child: Image.network(
                          '${notification.image}',
                          width: 50,
                          height: 50,
                          fit: BoxFit.cover,
                        ),
                      )
                    : ClipRRect(
                        borderRadius: BorderRadius.circular(10.0),
                        child: Image.asset(
                          'assets/images/icon.png',
                          width: 50,
                          height: 50,
                          fit: BoxFit.cover,
                        ),
                      ),
                title: Text(notification.title,
                    style: TextStyle(fontWeight: AppFontWeights.bold)),
                subtitle: Text(notification.message),
              );
            } else {
              // ✅ Pagination loading en bas
              return notificationController.isLoading.isTrue
                  ? const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Center(child: CupertinoActivityIndicator()),
                    )
                  : const SizedBox.shrink();
            }
          },
        );
      }
    });
  }
}

class NotificationListView extends StatelessWidget {
  final NotificationController notificationController;

  const NotificationListView({super.key, required this.notificationController});

  @override
  Widget build(BuildContext context) {
    return NotificationInfiniteScrollList(
        notificationController: notificationController);
  }
}

class NotificationInfiniteScrollList extends StatelessWidget {
  final NotificationController notificationController;
  final BadgeController badgeController = Get.find<BadgeController>();
  final ScrollController scrollController = ScrollController();

  NotificationInfiniteScrollList(
      {super.key, required this.notificationController}) {
    // Add a listener to detect when the user has scrolled to the bottom
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
              scrollController.position.maxScrollExtent &&
          !notificationController.isLoading.value) {
        // Load more notifications when at the bottom of the list
        notificationController.loadMoreNotifications();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: scrollController,
      itemCount: notificationController.notifications.length +
          1, // +1 for the loading indicator at the end
      itemBuilder: (context, index) {
        if (index < notificationController.notifications.length) {
          // Display a notification item
          final notification = notificationController.notifications[index];
          return ListTile(
            leading: notification.storeImageUrls.isNotEmpty
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(
                        10.0), // Ajustez la valeur pour le rayon des coins
                    child: Image.network(
                      '${notification.storeImageUrls.first}',
                      width: 50,
                      height: 50,
                      fit: BoxFit
                          .cover, // Cela permet à l'image de s'adapter à la taille définie
                    ),
                  )
                : ClipRRect(
                    borderRadius: BorderRadius.circular(
                        10.0), // Ajustez la valeur pour le rayon des coins
                    child: Image.asset(
                      'assets/images/icon.png',
                      width: 50,
                      height: 50,
                      fit: BoxFit
                          .cover, // Cela permet à l'image de s'adapter à la taille définie
                    ),
                  ),
            title: Text(
              notification.title,
              style: TextStyle(fontWeight: AppFontWeights.bold),
            ),
            subtitle: Text(notification.message),
            trailing: notification.isRead
                ? null
                : Icon(Icons.fiber_manual_record, color: Colors.red),
            onTap: () {
              // Mark notification as read when tapped
              notificationController.markAsRead(notification.id);
            },
          );
        } else {
          // Display a loading indicator at the bottom if more data is loading
          return notificationController.isLoading.isTrue
              ? Center(child: CircularProgressIndicator())
              : SizedBox.shrink();
        }
      },
    );
  }
}

class CustomTabButton extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onPressed;
  final int badgeCount;

  const CustomTabButton({
    required this.text,
    required this.isSelected,
    required this.onPressed,
    this.badgeCount = 0,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Barre du haut

          Container(
            height: 1,
            color: !isSelected ? Colors.grey[300] : AppColors.primary,
            width: double.infinity,
          ),

          Container(
            height: 44,
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 24),
            decoration: BoxDecoration(
              color: isSelected ? AppColors.primary : Colors.transparent,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  text,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                if (badgeCount > 0)
                  Container(
                    margin: EdgeInsets.only(left: 8),
                    padding: EdgeInsets.all(5),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                    child: Text(
                      badgeCount.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 8,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          // Barre du bas
          Container(
            height: 1,
            color: !isSelected ? Colors.grey[300] : AppColors.primary,
            width: double.infinity,
          ),
        ],
      ),
    );
  }
}

class ConversationPage extends StatefulWidget {
  final int itemId;
  final dynamic discussionId;
  final Map<String, dynamic>? item;
  final Map<String, dynamic>? user;
  final String interlocutor;
  final bool isStoreDiscussion;

  const ConversationPage({
    Key? key,
    this.user,
    required this.itemId,
    required this.item,
    required this.discussionId,
    required this.interlocutor,
    this.isStoreDiscussion = false,
  }) : super(key: key);

  @override
  State<ConversationPage> createState() => _ConversationPageState();
}

class _ConversationPageState extends State<ConversationPage> {
  final ConversationController conversationController =
      Get.put(ConversationController());
  final TextEditingController offerController = TextEditingController();
  final ScrollController scrollController = ScrollController();

  bool _isScrolling = false; // Flag to prevent multiple simultaneous scrolls
  Timer? _scrollTimer; // Timer for debouncing scroll calls

  @override
  void initState() {
    super.initState();

    // Load discussion details when the page loads
    _loadInitialData();

    // Listen to messages changes with debouncing
    ever(conversationController.messages, (_) {
      _debouncedScrollToBottom();
    });
  }

  @override
  void dispose() {
    _scrollTimer?.cancel();
    scrollController.dispose();
    super.dispose();
  }

  // Load initial data and scroll to bottom
  Future<void> _loadInitialData() async {
    await conversationController.loadDiscussionDetails(widget.discussionId);
    // Use jumpToBottom for initial load (no animation)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      jumpToBottom();
    });
  }

  // Debounced scroll to prevent too many scroll calls
  void _debouncedScrollToBottom() {
    _scrollTimer?.cancel();
    _scrollTimer = Timer(const Duration(milliseconds: 100), () {
      scrollToBottom();
    });
  }

  void scrollToBottom({bool animated = false}) {
    if (_isScrolling) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        final maxScrollExtent = scrollController.position.maxScrollExtent;
        final currentPosition = scrollController.position.pixels;

        if (maxScrollExtent - currentPosition > 20) {
          _isScrolling = true;

          if (animated) {
            scrollController
                .animateTo(
              maxScrollExtent,
              duration: const Duration(milliseconds: 400),
              curve: Curves.easeOutCubic,
            )
                .then((_) {
              _isScrolling = false;
            }).catchError((_) {
              _isScrolling = false;
            });
          } else {
            scrollController.jumpTo(maxScrollExtent);
            _isScrolling = false;
          }
        }
      }
    });
  }

  // Instant scroll to bottom (for initial load)
  void jumpToBottom() {
    if (scrollController.hasClients) {
      scrollController.jumpTo(scrollController.position.maxScrollExtent);
    }
  }

  // Function to make a phone call
  void _makePhoneCall(String? phoneNumber) async {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      Get.snackbar(
        'Error',
        'Phone number not available',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    final Uri url = Uri.parse('tel:$phoneNumber');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      Get.snackbar(
        'Error',
        'Could not launch phone call',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Get the appropriate phone number based on discussion type
  String? _getPhoneNumber() {
    if (widget.isStoreDiscussion) {
      // For store discussions, get the connected user's phone (the buyer)
      final connectedUserId = Get.find<AuthController>().user?.id;
      if (widget.item != null && widget.item!['user_id'] == connectedUserId) {
        return widget.user?['phone'];
      } else {
        return widget.item?['user']?['phone'];
      }
    } else {
      // For normal item discussions, get the other user's phone
      return widget.user?['phone'];
    }
  }

  // Handle sending messages
  void _handleSendMessage() {
    if (conversationController.isMakingOffer.value) {
      final offerValue =
          double.tryParse(conversationController.newMessage.value) ?? 0.0;
      if (offerValue > 0) {
        conversationController.offerAmount.value = offerValue;
        conversationController.addOfferMessage(
          widget.discussionId,
          offerValue,
        );
        conversationController.textController.clear();
        conversationController.isMakingOffer.value = false;
        FocusScope.of(context).unfocus();

        // Scroll to bottom after sending offer
        _debouncedScrollToBottom();
      }
    } else {
      conversationController.sendMessage(
          widget.discussionId, false, null, widget.isStoreDiscussion);

      // Scroll to bottom after sending message
      _debouncedScrollToBottom();
    }
  }

  @override
  Widget build(BuildContext context) {
    String imgurl =
        widget.item != null && widget.item!['images'][0]['value'] != null
            ? '${widget.item!['images'][0]['value']}'
            : '${widget.item!['images'][0]['url']}';

    bool userIsTheOwner = widget.item != null &&
        widget.item!['user_id'] == Get.find<AuthController>().user?.id;

    final InboxController inboxController = Get.find();

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        if (didPop) {
          // This will be called when the user navigates back
          // Either by pressing back button or swiping back on iOS/Android
          inboxController.fetchDiscussions();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              inboxController.fetchDiscussions();
              Get.back();
            },
          ),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  widget.interlocutor,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              IconButton(
                icon: Icon(Icons.phone, color: AppColors.primary, size: 20),
                onPressed: () => _makePhoneCall(_getPhoneNumber()),
              ),
            ],
          ),
        ),
        body: Column(
          children: [
            // Item details section
            if (widget.item != null)
              Container(
                padding: const EdgeInsets.all(16),
                color: Theme.of(context).dividerColor,
                child: Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        imgurl,
                        width: 50,
                        height: 50,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 50,
                            height: 50,
                            color: Colors.grey[300],
                            child: const Icon(Icons.image_not_supported),
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.item!['title'],
                            style: const TextStyle(fontWeight: FontWeight.bold),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text("${widget.item!['price']} mru"),
                        ],
                      ),
                    ),
                    if (!userIsTheOwner && !widget.isStoreDiscussion)
                      ElevatedButton(
                        onPressed: () {
                          conversationController.isMakingOffer.value = true;
                          conversationController.textController.clear();
                          FocusScope.of(context).unfocus();
                        },
                        style: ElevatedButton.styleFrom(
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text("Make an offer"),
                      )
                  ],
                ),
              ),

            // Messages list
            Expanded(
              child: Obx(() {
                if (conversationController.isloading.value &&
                    conversationController.messages.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CupertinoActivityIndicator(radius: 20),
                        SizedBox(height: 16),
                        Text(
                          'Loading messages...',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  controller: scrollController,
                  physics: const BouncingScrollPhysics(),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: conversationController.messages.length +
                      (conversationController.isloading.value ? 1 : 0),
                  itemBuilder: (context, index) {
                    // Loading indicator at bottom
                    if (index == conversationController.messages.length) {
                      return conversationController.isloading.value
                          ? const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Center(
                                child: CupertinoActivityIndicator(radius: 15),
                              ),
                            )
                          : const SizedBox.shrink();
                    }

                    final message = conversationController.messages[index];
                    final dateCategory = message.dateCategory ?? '';

                    bool showDateHeader = index == 0 ||
                        (message.dateCategory !=
                            conversationController
                                .messages[index - 1].dateCategory);

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Date header
                        if (showDateHeader)
                          Center(
                            child: Container(
                              margin: const EdgeInsets.symmetric(vertical: 8.0),
                              padding: const EdgeInsets.symmetric(
                                  vertical: 6.0, horizontal: 16.0),
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                dateCategory,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),

                        // Message bubble
                        message.isOffer
                            ? OfferBubble(
                                message: message,
                                itemName: widget.item!['title'])
                            : MessageBubble(message: message)
                      ],
                    );
                  },
                );
              }),
            ),

            // Message input section
            Obx(() {
              final isKeyboardOpen =
                  MediaQuery.of(context).viewInsets.bottom > 0;
              return Container(
                padding: EdgeInsets.only(
                  left: 16.0,
                  right: 8.0,
                  top: 8.0,
                  bottom: isKeyboardOpen ? 8.0 : 34.0,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  border: Border(
                    top: BorderSide(
                      color: Colors.grey[300]!,
                      width: 0.5,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(
                          maxHeight: 150.0,
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: TextField(
                            controller: conversationController.textController,
                            maxLines: 4,
                            minLines: 1,
                            keyboardType:
                                conversationController.isMakingOffer.value
                                    ? TextInputType.number
                                    : TextInputType.text,
                            onChanged: (value) {
                              conversationController.newMessage.value = value;
                            },
                            decoration: InputDecoration(
                              hintText:
                                  conversationController.isMakingOffer.value
                                      ? 'Enter your offer amount'
                                      : 'Type a message...',
                              border: InputBorder.none,
                              contentPadding: const EdgeInsets.symmetric(
                                  vertical: 12, horizontal: 16),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.send, color: Colors.white),
                        onPressed: _handleSendMessage,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}

class MessageBubble extends StatelessWidget {
  final Message message;

  MessageBubble({required this.message});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment:
          message.sentByMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Align(
          alignment:
              message.sentByMe ? Alignment.centerRight : Alignment.centerLeft,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width *
                  0.75, // Limite la largeur de la bulle à 75% de la largeur de l'écran
            ),
            child: Container(
              margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 14),
              decoration: BoxDecoration(
                color:
                    message.sentByMe ? AppColors.primary : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                message.content ?? '',
                style: TextStyle(
                  color: message.sentByMe ? Colors.white : Colors.black87,
                ),
              ),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Text(
            message.formattedTime,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface,
              fontSize: 10,
            ),
          ),
        ),
      ],
    );
  }
}

class OfferBubble extends StatelessWidget {
  final Message message;
  final String itemName;
  OfferBubble({required this.message, required this.itemName});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment:
          message.sentByMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Align(
          alignment:
              message.sentByMe ? Alignment.centerRight : Alignment.centerLeft,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width *
                  0.75, // Limite la largeur de la bulle à 75% de la largeur de l'écran
            ),
            child: Container(
              margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 14),
              decoration: BoxDecoration(
                color: Color(0xFFF07A13),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        message.sentByMe
                            ? 'You made an offer:'
                            : 'You received an offer of',
                        style: const TextStyle(
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        " ${message.price!.toStringAsFixed(0)} ",
                        style: const TextStyle(
                          color: Colors.black,
                          fontWeight: AppFontWeights.bold,
                        ),
                      ),
                      Text(
                        'MRU for',
                        style: TextStyle(
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    itemName,
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: AppFontWeights.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Text(
            message.formattedTime,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface,
              fontSize: 10,
            ),
          ),
        ),
      ],
    );
  }
}
